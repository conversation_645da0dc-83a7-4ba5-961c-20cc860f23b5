{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5173", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"idb": "^8.0.3", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}