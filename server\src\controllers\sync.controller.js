const Event = require('../models/Event');

/**
 * Bulk sync endpoint: accepts array of events
 * Each item: { eventId, type, timestamp, payload, source }
 */
exports.bulkSync = async (req, res) => {
    try {
        const events = Array.isArray(req.body) ? req.body : [];
        if (!events.length) {
            return res.json({ success: true, inserted: 0, duplicates: 0, errors: [] });
        }

        let inserted = 0, duplicates = 0;
        const errors = [];

        for (const ev of events) {
            try {
                // Validate required fields
                if (!ev.eventId || !ev.type || !ev.timestamp) {
                    errors.push({ eventId: ev.eventId, error: 'Missing required fields' });
                    continue;
                }

                await Event.create({
                    ...ev,
                    syncStatus: 'synced',
                    lastSyncedAt: new Date()
                });
                inserted++;
            } catch (e) {
                if (e.code === 11000) {
                    duplicates++;
                } else {
                    console.error('Insert error:', e.message, ev);
                    errors.push({ eventId: ev.eventId, error: e.message });
                }
            }
        }

        return res.json({
            success: true,
            inserted,
            duplicates,
            errors,
            total: events.length
        });

    } catch (err) {
        console.error('Bulk sync error:', err.message);
        return res.status(500).json({
            success: false,
            error: err.message,
            message: 'Internal Server Error'
        });
    }
};

/**
 * Get sync status and recent events
 */
exports.getStatus = async (req, res) => {
    try {
        const totalEvents = await Event.countDocuments();
        const recentEvents = await Event.find()
            .sort({ createdAt: -1 })
            .limit(10)
            .select('eventId type timestamp source syncStatus');

        return res.json({
            success: true,
            totalEvents,
            recentEvents,
            serverTime: new Date()
        });
    } catch (err) {
        console.error('Status error:', err.message);
        return res.status(500).json({
            success: false,
            error: err.message
        });
    }
};

