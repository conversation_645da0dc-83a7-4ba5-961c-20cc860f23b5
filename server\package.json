{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.17.1"}, "devDependencies": {"nodemon": "^3.1.10"}}