{"name": "once", "version": "1.4.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^7.0.1"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC"}