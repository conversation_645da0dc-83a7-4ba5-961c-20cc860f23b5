{"name": "oms-electron", "version": "1.0.0", "description": "Electron wrapper for OMS Offline-First Application", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["oms", "offline", "electron", "organization", "management"], "author": "OMS Team", "license": "MIT", "dependencies": {"electron": "^37.2.6"}, "devDependencies": {"electron-builder": "^25.1.8"}, "build": {"appId": "com.oms.offline", "productName": "OMS Offline", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "fileQueue.js", "../client/dist/**/*", "../server/src/**/*", "../server/package.json"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}