{"version": 3, "sources": ["../../src/factories/createMockLogger.js"], "names": ["createMockLogger", "onMessage", "parentContext", "log", "a", "b", "c", "d", "e", "f", "g", "h", "i", "k", "adopt", "routine", "child", "context", "getContext", "logLevel", "Object", "keys", "logLevels"], "mappings": ";;;;;;;AAEA;;AAUA,MAAMA,gBAAgB,GAAG,CAACC,SAAD,EAAqCC,aAArC,KAAwF;AAC/G;AACA,QAAMC,GAAG,GAAG,CAACC,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B,KAAkC,CAC5C;AACD,GAFD;;AAIAV,EAAAA,GAAG,CAACW,KAAJ,GAAY,MAAOC,OAAP,IAAmB;AAC7B,WAAOA,OAAO,EAAd;AACD,GAFD,CAN+G,CAU/G;;;AACAZ,EAAAA,GAAG,CAACa,KAAJ,GAAaC,OAAD,IAA4E;AACtF,WAAOjB,gBAAgB,CAACC,SAAD,EAAYC,aAAZ,CAAvB;AACD,GAFD;;AAIAC,EAAAA,GAAG,CAACe,UAAJ,GAAiB,MAA0B;AACzC,WAAO,EAAP;AACD,GAFD;;AAIA,OAAK,MAAMC,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYC,oBAAZ,CAAvB,EAA+C;AAC7C;AACAnB,IAAAA,GAAG,CAACgB,QAAD,CAAH,GAAgB,CAACf,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B,KAAkC;AAChD,aAAOV,GAAG,CAACa,KAAJ,CAAU;AACfG,QAAAA,QAAQ,EAAEG,qBAAUH,QAAV;AADK,OAAV,EAEJf,CAFI,EAEDC,CAFC,EAEEC,CAFF,EAEKC,CAFL,EAEQC,CAFR,EAEWC,CAFX,EAEcC,CAFd,EAEiBC,CAFjB,EAEoBC,CAFpB,EAEuBC,CAFvB,CAAP;AAGD,KAJD;AAKD,GA1B8G,CA4B/G;AACA;;;AACA,SAAOV,GAAP;AACD,CA/BD;;eAiCeH,gB", "sourcesContent": ["// @flow\n\nimport {\n  logLevels,\n} from '../constants';\nimport type {\n  LoggerType,\n  MessageContextType,\n  MessageEventHandlerType,\n  TranslateMessageFunctionType,\n} from '../types';\n\nconst createMockLogger = (onMessage: MessageEventHandlerType, parentContext?: MessageContextType): LoggerType => {\n  // eslint-disable-next-line id-length, unicorn/prevent-abbreviations, no-unused-vars\n  const log = (a, b, c, d, e, f, g, h, i, k) => {\n    //\n  };\n\n  log.adopt = async (routine) => {\n    return routine();\n  };\n\n  // eslint-disable-next-line no-unused-vars\n  log.child = (context: TranslateMessageFunctionType | MessageContextType): LoggerType => {\n    return createMockLogger(onMessage, parentContext);\n  };\n\n  log.getContext = (): MessageContextType => {\n    return {};\n  };\n\n  for (const logLevel of Object.keys(logLevels)) {\n    // eslint-disable-next-line id-length, unicorn/prevent-abbreviations\n    log[logLevel] = (a, b, c, d, e, f, g, h, i, k) => {\n      return log.child({\n        logLevel: logLevels[logLevel],\n      })(a, b, c, d, e, f, g, h, i, k);\n    };\n  }\n\n  // @see https://github.com/facebook/flow/issues/6705\n  // $FlowFixMe\n  return log;\n};\n\nexport default createMockLogger;\n"], "file": "createMockLogger.js"}