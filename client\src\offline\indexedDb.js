import { openDB } from 'idb';

const DB_NAME = 'oms-offline';
const STORE = 'events';

const dbPromise = openDB(DB_NAME, 1, {
  upgrade(db) {
    if(!db.objectStoreNames.contains(STORE)) {
         db.createObjectStore(STORE, { keyPath: 'eventId', autoIncrement: true });
    }
  },
});

export async function addEvent(event) {
  const db = await dbPromise;
  await db.add(STORE, event);
}

export async function getEvents() {
  const db = await dbPromise;
  return await db.getAll(STORE);
}

export async function deleteEvent(eventId) {
  const db = await dbPromise;
  await db.delete(STORE, eventId);
}

export async function clearAll() {
  const db = await dbPromise;
  const tx = db.transaction(STORE, 'readwrite');
  await tx.store.clear();
  await tx.done;
}
