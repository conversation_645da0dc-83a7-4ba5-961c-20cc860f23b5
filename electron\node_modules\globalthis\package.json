{"name": "globalthis", "version": "1.0.4", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ECMAScript spec-compliant polyfill/shim for `globalThis`", "license": "MIT", "main": "index.js", "browser": {"./implementation": "./implementation.browser.js"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest && npm run build", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run --silent tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound --property", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/System.global.git"}, "keywords": ["window", "self", "global", "globalThis", "System.global", "global object", "global this value", "ECMAScript", "es-shim API", "polyfill", "shim"], "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "devDependencies": {"@es-shims/api": "^2.5.0", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "browserify": "^16.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": ["browserShim.js", ".github/workflows"]}}