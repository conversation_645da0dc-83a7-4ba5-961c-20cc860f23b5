import React, { useEffect, useState } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Chip,
  Alert
} from '@mui/material';
import {
  Business as BusinessIcon,
  CloudOff as OfflineIcon,
  Cloud as OnlineIcon
} from '@mui/icons-material';
import EventButtons from './components/EventButtons';
import { setupAutoSync, getSyncStatus } from './offline/syncService';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const App = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState({ syncing: false });

  useEffect(() => {
    // Setup offline sync
    setupAutoSync();

    // Monitor online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Update sync status periodically
    const statusInterval = setInterval(() => {
      setSyncStatus(getSyncStatus());
    }, 1000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(statusInterval);
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <BusinessIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            OMS - Organization Management System
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip
              icon={isOnline ? <OnlineIcon /> : <OfflineIcon />}
              label={isOnline ? 'Online' : 'Offline'}
              color={isOnline ? 'success' : 'warning'}
              variant="outlined"
              size="small"
              sx={{ color: 'white', borderColor: 'white' }}
            />
            {syncStatus.syncing && (
              <Chip
                label="Syncing..."
                color="info"
                variant="outlined"
                size="small"
                sx={{ color: 'white', borderColor: 'white' }}
              />
            )}
          </Box>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          Offline-First Organization Management
        </Typography>

        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
          Track check-ins, breaks, tasks, and productivity data. Works offline and syncs automatically when online.
        </Typography>

        {!isOnline && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            You are currently offline. All actions will be saved locally and synced when connection is restored.
          </Alert>
        )}

        <EventButtons />

        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Built with React, Material-UI, IndexedDB, and Express.js
          </Typography>
        </Box>
      </Container>
    </ThemeProvider>
  );
};

export default App;
