import isElectron from "../utils/isElectron";
import {putEvent, getAllEvents, deleteEvent } from "./indexedDb";

/**
 * Unified queue API. In web -> IndexedDB. In Electron -> delegate to preload (file queue).
 */
export const Queue = {
  async enqueue(ev) {
    if (isElectron() && window.electron?.queueEvent) {
      return window.electron.queueEvent(ev);
    }
    return putEvent(ev);
  },
  async readAll() {
    if (isElectron() && window.electron?.readQueue) {
      return window.electron.readQueue();
    }
    return getAllEvents();
  },
  async remove(id) {
    if (isElectron() && window.electron?.removeFromQueue) {
      return window.electron.removeFromQueue(id);
    }
    return deleteEvent(id);
  }
};