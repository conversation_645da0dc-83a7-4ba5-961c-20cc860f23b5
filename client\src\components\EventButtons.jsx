import React from 'react';
import { enqueueEvent, trySync } from '../offline/syncService';

function uid() {
  return 'evt-' + Math.random().toString(36).slice(2) + Date.now();
}

export default function EventButtons() {
  const send = async (type, payload = {}) => {
    const ev = { eventId: uid(), type, payload };
    await enqueueEvent(ev);
    await trySync();
  };

  return (
    <div style={{ display: 'grid', gap: 8 }}>
      <button onClick={() => send('checkin', { userId: 'u1' })}>Check In</button>
      <button onClick={() => send('checkout', { userId: 'u1' })}>Check Out</button>
      <button onClick={() => send('breakStart', { userId: 'u1', kind: 'tea' })}>Break Start</button>
      <button onClick={() => send('breakEnd', { userId: 'u1' })}>Break End</button>
      <button onClick={() => send('taskUpdate', { taskId: 'T-123', status: 'in-progress' })}>Task Update</button>
      <button onClick={() => send('productivity', { userId: 'u1', score: 85 })}>Productivity</button>
      <small>Go offline (disable network), click buttons, then go online to auto-sync.</small>
    </div>
  );
}