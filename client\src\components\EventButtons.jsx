import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Login as CheckInIcon,
  Logout as CheckOutIcon,
  Coffee as BreakIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  CheckCircle as CompleteIcon,
  Assessment as ProductivityIcon
} from '@mui/icons-material';
import { enqueueEvent, trySync, addSyncListener, removeSyncListener, getSyncStatus } from '../offline/syncService';
import { getEventStats } from '../offline/indexedDb';

function generateEventId() {
  return `evt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
}

export default function EventButtons() {
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [onBreak, setOnBreak] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [syncStatus, setSyncStatus] = useState({ syncing: false, online: true });
  const [eventStats, setEventStats] = useState({ total: 0, pending: 0, synced: 0 });
  const [lastSync, setLastSync] = useState(null);

  useEffect(() => {
    // Load initial stats
    loadEventStats();

    // Setup sync listener
    const handleSyncEvent = (event) => {
      if (event.type === 'sync_start') {
        setSyncStatus(prev => ({ ...prev, syncing: true }));
      } else if (event.type === 'sync_complete') {
        setSyncStatus(prev => ({ ...prev, syncing: false }));
        setLastSync(new Date());
        loadEventStats();
      } else if (event.type === 'sync_error') {
        setSyncStatus(prev => ({ ...prev, syncing: false }));
      }
    };

    addSyncListener(handleSyncEvent);

    // Update online status
    const updateOnlineStatus = () => {
      setSyncStatus(prev => ({ ...prev, online: navigator.onLine }));
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      removeSyncListener(handleSyncEvent);
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const loadEventStats = async () => {
    try {
      const stats = await getEventStats();
      setEventStats(stats);
    } catch (error) {
      console.error('Failed to load event stats:', error);
    }
  };

  const sendEvent = async (type, payload = {}) => {
    const event = {
      eventId: generateEventId(),
      type,
      payload: {
        userId: 'user-1',
        timestamp: new Date().toISOString(),
        ...payload
      }
    };

    try {
      await enqueueEvent(event);
      await loadEventStats();

      // Update local state based on event type
      switch (type) {
        case 'checkin':
          setIsCheckedIn(true);
          break;
        case 'checkout':
          setIsCheckedIn(false);
          setOnBreak(false);
          setCurrentTask(null);
          break;
        case 'breakStart':
          setOnBreak(true);
          break;
        case 'breakEnd':
          setOnBreak(false);
          break;
        case 'taskStart':
          setCurrentTask({ id: payload.taskId, status: 'in-progress' });
          break;
        case 'taskComplete':
          setCurrentTask(null);
          break;
      }
    } catch (error) {
      console.error('Failed to send event:', error);
    }
  };

  const handleManualSync = async () => {
    try {
      await trySync();
    } catch (error) {
      console.error('Manual sync failed:', error);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, margin: '0 auto', padding: 2 }}>
      {/* Status Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
            <Chip
              label={syncStatus.online ? 'Online' : 'Offline'}
              color={syncStatus.online ? 'success' : 'warning'}
              size="small"
            />
            <Chip
              label={isCheckedIn ? 'Checked In' : 'Checked Out'}
              color={isCheckedIn ? 'success' : 'default'}
              size="small"
            />
            <Chip
              label={onBreak ? 'On Break' : 'Working'}
              color={onBreak ? 'warning' : 'primary'}
              size="small"
            />
            {currentTask && (
              <Chip
                label={`Task: ${currentTask.id}`}
                color="info"
                size="small"
              />
            )}
          </Box>

          <Typography variant="body2" color="text.secondary">
            Events: {eventStats.total} total, {eventStats.pending} pending, {eventStats.synced} synced
            {lastSync && ` • Last sync: ${lastSync.toLocaleTimeString()}`}
          </Typography>

          {eventStats.pending > 0 && (
            <Alert severity="info" sx={{ mt: 1 }}>
              {eventStats.pending} events waiting to sync
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Grid container spacing={2}>
        {/* Check In/Out */}
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Attendance
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant={isCheckedIn ? "outlined" : "contained"}
                  color="success"
                  startIcon={<CheckInIcon />}
                  onClick={() => sendEvent('checkin')}
                  disabled={isCheckedIn}
                  fullWidth
                >
                  Check In
                </Button>
                <Button
                  variant={!isCheckedIn ? "outlined" : "contained"}
                  color="error"
                  startIcon={<CheckOutIcon />}
                  onClick={() => sendEvent('checkout')}
                  disabled={!isCheckedIn}
                  fullWidth
                >
                  Check Out
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Break Management */}
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Break Management
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant={onBreak ? "outlined" : "contained"}
                  color="warning"
                  startIcon={<BreakIcon />}
                  onClick={() => sendEvent('breakStart', { breakType: 'general' })}
                  disabled={!isCheckedIn || onBreak}
                  fullWidth
                >
                  Start Break
                </Button>
                <Button
                  variant={!onBreak ? "outlined" : "contained"}
                  color="primary"
                  startIcon={<StopIcon />}
                  onClick={() => sendEvent('breakEnd')}
                  disabled={!onBreak}
                  fullWidth
                >
                  End Break
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Task Management */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Task Management
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<StartIcon />}
                  onClick={() => sendEvent('taskStart', { taskId: `TASK-${Date.now()}`, title: 'New Task' })}
                  disabled={!isCheckedIn || onBreak}
                >
                  Start Task
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  startIcon={<PauseIcon />}
                  onClick={() => sendEvent('taskPause', { taskId: currentTask?.id })}
                  disabled={!currentTask}
                >
                  Pause Task
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<StopIcon />}
                  onClick={() => sendEvent('taskStop', { taskId: currentTask?.id })}
                  disabled={!currentTask}
                >
                  Stop Task
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<CompleteIcon />}
                  onClick={() => sendEvent('taskComplete', { taskId: currentTask?.id })}
                  disabled={!currentTask}
                >
                  Complete Task
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Productivity & Sync */}
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Productivity
              </Typography>
              <Button
                variant="contained"
                color="info"
                startIcon={<ProductivityIcon />}
                onClick={() => sendEvent('productivity', {
                  score: Math.floor(Math.random() * 100) + 1,
                  metrics: { focus: 85, efficiency: 92 }
                })}
                disabled={!isCheckedIn}
                fullWidth
              >
                Log Productivity
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sync Control
              </Typography>
              <Button
                variant="outlined"
                onClick={handleManualSync}
                disabled={syncStatus.syncing || !syncStatus.online}
                startIcon={syncStatus.syncing ? <CircularProgress size={20} /> : null}
                fullWidth
              >
                {syncStatus.syncing ? 'Syncing...' : 'Manual Sync'}
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Instructions */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Testing Offline Mode:</strong> Disable your network connection, perform actions,
          then re-enable network to see automatic synchronization.
        </Typography>
      </Alert>
    </Box>
  );
}