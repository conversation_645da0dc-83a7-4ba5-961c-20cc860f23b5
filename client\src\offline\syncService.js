import { Queue } from './queueAdapter';
import { bulkSync } from '../api/syncApi';

let syncing = false;

export async function enqueueEvent(ev) {
  // ensure eventId + timestamp exist
  const withMeta = {
    source: window?.electron ? 'electron' : 'web',
    timestamp: ev.timestamp || new Date().toISOString(),
    ...ev
  };
  await Queue.enqueue(withMeta);
}

export async function trySync() {
  if (syncing) return; // avoid concurrent sync
  const isOnline = navigator.onLine;
  if (!isOnline) return;
  syncing = true;
  try {
    const items = await Queue.readAll();
    if (!items.length) return;

    // Sort by timestamp to keep order
    items.sort((a,b) => new Date(a.timestamp) - new Date(b.timestamp));

    const batchSize = 100; // avoid giant payloads
    for (let i = 0; i < items.length; i += batchSize) {
      const slice = items.slice(i, i + batchSize);
      await bulkSync(slice);
      // remove sent
      for (const ev of slice) {
        await Queue.remove(ev.eventId);
      }
    }
  } finally {
    syncing = false;
  }
}

export function setupOnlineSync() {
  // On online event
  window.addEventListener('online', () => trySync());
  // On load
  window.addEventListener('load', () => trySync());
}