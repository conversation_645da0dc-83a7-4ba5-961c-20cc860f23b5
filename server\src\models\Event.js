const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
    eventId: { type: String, unique: true, required: true, index: true }, // idempotency key
    type: { type: String, required: true, enum: [
        'checkin',
        'checkout',
        'breakStart',
        'breakEnd',
        'taskStart',
        'taskPause',
        'taskStop',
        'taskComplete',
        'productivity'
    ]},
    timestamp: { type: Date, required: true },
    payload: { type: mongoose.Schema.Types.Mixed, required: true },
    source: { type: String, required: true, enum: ['electron', 'web'], default: 'web' },
    syncStatus: { type: String, enum: ['pending', 'synced'], default: 'synced' },
    lastSyncedAt: { type: Date, default: Date.now }
}, { timestamps: true });

// Ensure eventId is unique
eventSchema.index({ eventId: 1 }, { unique: true });

module.exports = mongoose.model('Event', eventSchema);
